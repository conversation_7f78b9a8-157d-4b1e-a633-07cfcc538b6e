import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import { InputField, CurrencyInputField, SwitchField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import { numberFormat } from '@/lib/utils';

interface FormData {
  id: number;
  generalPrice: string;
  staffPrice: string;
  isAvailable: boolean;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [editGeneralPrice, setEditGeneralPrice] = useState(false);
  const [editStaffPrice, setEditStaffPrice] = useState(false);

  const form = useForm<FormData>({
    defaultValues: {
      generalPrice: data?.generalPrice,
      staffPrice: data?.staffPrice,
      isAvailable: data?.isAvailable,
    },
  });

  useEffect(() => {
    if (data) {
      form.reset(data);
    }
  }, [data, form]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/cafeteria/menu/update`, {
        menuId: data?.id,
        isAvailable: data.isAvailable,
        generalPrice: Number(data?.generalPrice),
        staffPrice: Number(data?.staffPrice)
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setOpen(false);
        setEditGeneralPrice(false);
        setEditStaffPrice(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Reward Details"
      description="View and manage reward details"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <div>
              <h3 className="text-sm font-medium">Name</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.name}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Category</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.menuCategory.name}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">
                General Price{' '}
                <span
                  onClick={() => setEditGeneralPrice(!editGeneralPrice)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editGeneralPrice ? (
                <CurrencyInputField
                  control={form.control}
                  name="generalPrice"
                  label="Enter a new price"
                  placeholder=""
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {numberFormat(data?.generalPrice)}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Staff Price{' '}
                <span
                  onClick={() => setEditStaffPrice(!editStaffPrice)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editStaffPrice ? (
                <CurrencyInputField
                  control={form.control}
                  name="staffPrice"
                  label="Enter a new price"
                  placeholder=""
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                 {numberFormat(data?.staffPrice)}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">Date Created</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.createdAt
                  ? dayjs(data.createdAt).format('MMMM D, YYYY')
                  : '-'}
              </p>
            </div>
          </FormRow>
          <SwitchField
            control={form.control}
            name="isAvailable"
            label="Is Available?"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default Details;
